﻿@page "/enddayreports/details"
@using Microsoft.EntityFrameworkCore
@using EndDayReports.model
@using System.Globalization
@inject IDbContextFactory<EndDayReports.Data.EndDayReportsContext> DbFactory
@inject NavigationManager NavigationManager
@inject IConfiguration Configuration
@inject IJSRuntime JS

<PageTitle>End Day Reports</PageTitle>

<div class="container mt-4">
	<h1 class="mb-4">End Day Report Details</h1>

<nav class="navbar navbar-expand-lg navbar-light bg-light sticky-top border-bottom shadow-sm mb-4">
    <div class="container-fluid">
        <ul class="nav nav-pills">
			<li class="nav-item">
                <a class="nav-link" href="#"
                   @onclick='() => ScrollToSection("employee-section")'
                   @onclick:preventDefault="true">Employee</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#"
                   @onclick='() => ScrollToSection("totals-section")'
                   @onclick:preventDefault="true">Totals</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#"
                   @onclick='() => ScrollToSection("pumps-section")'
                   @onclick:preventDefault="true">Pumps</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#"
                   @onclick='() => ScrollToSection("notes-section")'
                   @onclick:preventDefault="true">Notes</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#"
                   @onclick='() => ScrollToSection("products-section")'
                   @onclick:preventDefault="true">Products</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#"
                   @onclick='() => ScrollToSection("taxes-section")'
                   @onclick:preventDefault="true">Taxes</a>
            </li>
			<li class="nav-item">
                <a class="nav-link" href="#"
                   @onclick='() => ScrollToSection("cashier-section")'
                   @onclick:preventDefault="true">Cashiers</a>
            </li>
        </ul>
    </div>
</nav>




	@if (enddayreport is null)
	{
		<p><em>Loading...</em></p>
	}
	else
	{
		<div id="employee-section">
			<!-- Employee Info Table -->
			<h3 class ="section-title">Employee Info</h3>

			<div class="input-group mb-3">
				<span class="input-group-text col-sm-3">Created By</span>
				<div class="form-control col-sm-8 bg-light border-0">
					@enddayreport.CreatedByEmployeeName
				</div>
			</div>
			<div class="input-group mb-3">
				<span class="input-group-text col-sm-3">Created Date</span>
				<div class="form-control col-sm-8 bg-light border-0">
					@enddayreport.CreatedDateTime
				</div>
			</div>
			<div class="input-group mb-3">
				<span class="input-group-text col-sm-3">Shift Time Start</span>
				<div class="form-control col-sm-8 bg-light border-0">
					@enddayreport.ShiftTimeStart.ToString(@"hh\:mm")
				</div>
			</div>
			<div class="input-group mb-3">
				<span class="input-group-text col-sm-3">Shift Time End</span>
				<div class="form-control col-sm-8 bg-light border-0">
					@enddayreport.ShiftTimeEnd.ToString(@"hh\:mm")
				</div>
			</div>
		</div>
		<br />
		<!-- Totals Table -->
		<div class="container mt-3" id="totals-section">
			<h3 class="section-title">Totals</h3>
			<table class="table table-hover">
				<thead>
					<tr>
						<th>Section</th>
						<th class="text-end">Dollars</th>
					</tr>
				</thead>
				<tbody>
					<tr>
						<td>Total Sales</td>
						<td class="text-end">@enddayreport.TotalProductsSales.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td>
					</tr>
					<tr>
						<td>Total Credits/Debits</td>
						<td class="text-end">@enddayreport.TotalCredits.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td>
					</tr>
					<tr>
						<td>Total Cashier Over/Under</td>
						<td class="text-end">@enddayreport.TotalOverUnder.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td>
					</tr>

				</tbody>
			</table>
		</div>
		<br />
		<!-- Pump Sales Table -->
		<div class="container mt-3" id="pumps-section">
			<h3 class="section-title">Pumps</h3>
			<table class="table table-hover">
				<thead>
					<tr>
						<th>Pump</th>
						<th class ="text-center">Gallons</th>
						<th class="text-end">Dollars</th>
					</tr>
				</thead>
				<tbody>
					<tr>
						<td>K1</td>
						<td class="text-center">@enddayreport.PumpK1Gallons</td>
						<td class="text-end">@enddayreport.PumpK1Dollars.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td>
					</tr>
					<tr>
						<td>Off Road Diesel</td>
						<td class="text-center">@enddayreport.PumpOffRoadDieselGallons</td>
						<td class="text-end">@enddayreport.PumpOffRoadDieselDollars.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td>
					</tr>
					<tr>
						<td>Unleaded Premium</td>
						<td class="text-center">@enddayreport.PumpUnleadedPremiumGallons</td>
						<td class="text-end">@enddayreport.PumpUnleadedPremiumDollars.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td>
					</tr>
					<tr>
						<td>Unleaded Plus</td>
						<td class="text-center">@enddayreport.PumpUnleadedPlusGallons</td>
						<td class="text-end">@enddayreport.PumpUnleadedPlusDollars.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td>
					</tr>
					<tr>
						<td>Unleaded Regular</td>
						<td class="text-center">@enddayreport.PumpUnleadedRegularGallons</td>
						<td class="text-end">@enddayreport.PumpUnleadedRegularDollars.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td>
					</tr>
					<tr>
						<td>Diesel</td>
						<td class="text-center">@enddayreport.PumpDieselGallons</td>
						<td class="text-end">@enddayreport.PumpDieselDollars.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td>
					</tr>
					<tr class="fw-bold table-secondary">
						<td>Sub Total</td>
						<td class="text-center">@enddayreport.TotalPumpGallons</td>
						<td class="text-end">@enddayreport.TotalPumpSales.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td>
					</tr>
				</tbody>
			</table>
		</div>
		<!-- Notes Section -->
		<div class="container mt-3" id="notes-section">
			<h3 class="section-title">Additional</h3>
			<div class="mb-3">
				<label for="notes" class="form-label">Additional Notes</label>
				<InputTextArea id="notes"
							   class="form-control"
							   @bind-Value="enddayreport.Notes"
							   rows="5"
							   readonly
							   disabled
							   />
			</div>
		</div>
		<!-- Side-by-side layout for Products and Taxes/Credits -->
		<div class="container mt-3">
			<div class="row">
				<!-- Products Table (Left) -->
				<div class="col-md-6">
					<div id="products-section">
						<h3 class="section-title">Products</h3>
						<table class="table table-hover">
							<thead>
								<tr>
									<th>Product</th>
									<th class="text-end">Dollars</th>
								</tr>
							</thead>
							<tbody>
								<tr><td>Ice</td><td class="text-end">@enddayreport.Ice.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td></tr>
								<tr><td>Beer</td><td class="text-end">@enddayreport.Beer.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td></tr>
								<tr><td>Groceries</td><td class="text-end">@enddayreport.Groceries.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td></tr>
								<tr><td>Deli</td><td class="text-end">@enddayreport.Deli.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td></tr>
								<tr><td>Cigarettes</td><td class="text-end">@enddayreport.Cigarettes.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td></tr>
								<tr><td>Meat</td><td class="text-end">@enddayreport.Meat.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td></tr>
								<tr><td>NonFood</td><td class="text-end">@enddayreport.NonFood.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td></tr>
								<tr><td>Donuts</td><td class="text-end">@enddayreport.Donuts.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td></tr>
								<tr><td>Gasoline</td><td class="text-end">@enddayreport.TotalPumpSales.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td></tr>
								<tr><td>Chicken</td><td class="text-end">@enddayreport.Chicken.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td></tr>
								<tr><td>HBA</td><td class="text-end">@enddayreport.HBA.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td></tr>
								<tr><td>Lotto</td><td class="text-end">@enddayreport.Lotto.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td></tr>
								<tr><td>Pizza</td><td class="text-end">@enddayreport.Pizza.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td></tr>
								<tr><td>MoneyOrder</td><td class="text-end">@enddayreport.MoneyOrder.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td></tr>
								<tr><td>Lottery</td><td class="text-end">@enddayreport.Lottery.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td></tr>
								<tr><td>FarmTax</td><td class="text-end">@enddayreport.FarmTax.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td></tr>
								<tr><td>SalesTax</td><td class="text-end">@enddayreport.SalesTax.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td></tr>

								<tr class="fw-bold table-secondary"><td>Sub Total</td><td class="text-end">@enddayreport.TotalProductsSales.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td></tr>
							</tbody>
						</table>
					</div>
					<div id="cashier-section">
						<h3 class="section-title">Over/Under</h3>
						<table class="table table-hover mb-4">
							<thead>
								<tr>
									<th>Cashier</th>
									<th></th>
									<th class="text-end">Amount</th>
								</tr>
							</thead>
							<tbody>
								@foreach (var entry in enddayreport.OverUnderEntries)
								{
									<tr>
										<td>@entry.CashierName</td>
										<td></td>
										<td class="text-end">@entry.OverAmount.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td>
									</tr>
								}

								@if (enddayreport.OverUnderEntries.Any())
								{
									<tr class="fw-bold table-secondary">
										<td>Sub Total</td>
										<td colspan="3" class="text-end">
											@enddayreport.OverUnderEntries.Sum(e => e.Total).ToString("C", CultureInfo.GetCultureInfo("en-US"))
										</td>
										<td></td>
									</tr>
								}
							</tbody>
						</table>
					</div>
				</div>

				<!-- Taxes and Less Credits Table (Right) -->
				<div class="col-md-6">
					<div id="tenders-section">
						<h3 class="section-title">Tender</h3>
						<table class="table table-hover">
							<thead>
								<tr>
									<th>Credits</th>
									<th class="text-end"></th>
									<th class="text-end">Dollars</th>
								</tr>
							</thead>
							<tbody>
								<tr><td>Credit Card</td><td /><td class="text-end">@enddayreport.CreditCard.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td></tr>
								<tr><td>Debit Card</td><td /><td class="text-end">@enddayreport.DebitCard.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td></tr>
								<tr><td>Food Stamps</td><td /><td class="text-end">@enddayreport.FoodStamps.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td></tr>
								<tr><td>Coupons</td><td /><td class="text-end">@enddayreport.Coupons.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td></tr>
								<tr><td>Approved Credit</td><td>@if (enddayreport.CashDepositSlipPdf is not null)
										{
											<a class="btn btn-outline-primary" href="@($"api/reports/{enddayreport.Id}/download/approvedcredit")" target="_blank">View Slip</a>
										}</td><td class="text-end">@enddayreport.ApprovedCredit.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td></tr>
								<tr><td>Paid Outs</td><td>@if (enddayreport.PaidOutDepositSlipPdf is not null)
										{
											<a class="btn btn-outline-primary" href="@($"api/reports/{enddayreport.Id}/download/paidout")" target="_blank">View Slip</a>
										}</td><td class="text-end">@enddayreport.PaidOuts.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td></tr>
								<tr><td>Void And Refund</td><td /><td class="text-end">@enddayreport.VoidAndRefund.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td></tr>
								<tr><td>ATM</td><td /><td class="text-end">@enddayreport.ATM.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td></tr>
								<tr><td>Telecheck</td><td /><td class="text-end">@enddayreport.Telecheck.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td></tr>
								<tr><td>Cash</td><td>@if (enddayreport.CashDepositSlipPdf is not null)
										{
											<a class="btn btn-outline-primary" href="@($"api/reports/{enddayreport.Id}/download/cash")" target="_blank">View Slip</a>
										}</td><td class="text-end">@enddayreport.Cash.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td></tr>
								<tr><td>Check</td><td>@if (enddayreport.CashDepositSlipPdf is not null)
										{
											<a class="btn btn-outline-primary" href="@($"api/reports/{enddayreport.Id}/download/check")" target="_blank">View Slip</a>
										}</td><td class="text-end">@enddayreport.Check.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td></tr>
								<tr class="fw-bold table-secondary"><td>Sub Total</td><td /><td class="text-end">@enddayreport.TotalCredits.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td></tr>
							</tbody>
						</table>
					</div>

					<div id="taxes-section">
						<h3 class="section-title w-100">Tax Report</h3>
							<table class="table table-hover">
								<thead class="double-height-header">
									<tr>
										<th class="text-start text-nowrap fw-bold w-10">Name</th>
										<th class="text-end text-nowrap fw-bold w-20">Tax-Rate</th>
										<th class="text-end text-nowrap fw-bold w-35">Taxable Sales</th>
										<th class="text-end text-nowrap fw-bold w-35">Non-Taxable</th>
									</tr>
								</thead>
								 <tbody>
									<tr>
										<td class="align-middle text-nowrap w-5">Low Tax</td>
										<td class="text-end align-middle w-5">@LowTaxRateDisplay</td>
										<td class="text-end align-middle w-45">@enddayreport.LowTaxTaxableSales.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td>
										<td class="text-end align-middle text-nowrap w-45">@enddayreport.LowTaxNonTaxableSales.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td>
									</tr>
									<tr>
										<td class="align-middle text-nowrap w-5">High Tax</td>
										<td class="text-end align-middle w-5">@HighTaxRateDisplay</td>
										<td class="text-end align-middle w-45">@enddayreport.HighTaxTaxableSales.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td>
										<td class="text-end  align-middle w-45">@enddayreport.HighTaxNonTaxableSales.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td>
									</tr>
								</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>

		<div class="mt-4">
			<a class="btn btn-secondary me-2" href="@($"/enddayreports/edit?id={enddayreport.Id}")">Edit</a>
			<a class="btn btn-primary" href="@($"/enddayreports")">Back to List</a>
		</div>
	}
</div>

@code {
	private EndDayReport? enddayreport;

	[SupplyParameterFromQuery]
	private int Id { get; set; }

	 private decimal LowTaxRate { get; set; }
    private decimal HighTaxRate { get; set; }
    private string LowTaxRateDisplay => $"{LowTaxRate:P3}";
    private string HighTaxRateDisplay => $"{HighTaxRate:P3}";

    protected override async Task OnInitializedAsync()
    {
        // Load tax rates from configuration
        LowTaxRate = Configuration.GetValue<decimal>("TaxRates:Low");
        HighTaxRate = Configuration.GetValue<decimal>("TaxRates:High");
		
		// Load report from database
		using var context = DbFactory.CreateDbContext();
		enddayreport ??= await context.EndDayReport
            .Include(r => r.OverUnderEntries)
            .FirstOrDefaultAsync(m => m.Id == Id);

		if (enddayreport is null)
		{
			NavigationManager.NavigateTo("notfound");
		}
	}

	private async Task ScrollToSection(string sectionId)
    {
        await JS.InvokeVoidAsync("scrollToSection", sectionId);
    }
}


<script>
    window.scrollToSection = (id) => {
        const el = document.getElementById(id);
        if (el) {
            el.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    };
</script>
