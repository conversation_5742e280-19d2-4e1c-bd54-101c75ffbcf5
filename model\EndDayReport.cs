﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;


namespace EndDayReports.model
{
    [Table("EndDayReports", Schema = "EoD")]
    public class EndDayReport : IValidatableObject
    {
        public int Id { get; set; }

        //[Required(ErrorMessage = "Employee name is required.")]
        public string CreatedByEmployeeName { get; set; } = string.Empty;
        //public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime CreatedDateTime { get; set; } = DateTime.UtcNow;
        public TimeSpan ShiftTimeStart { get; set; }
        public TimeSpan ShiftTimeEnd { get; set; }

        public string ShiftTimeStartStr
        {
            get => ShiftTimeStart.ToString(@"hh\:mm");
            set => ShiftTimeStart = TimeSpan.TryParse(value, out var ts) ? ts : TimeSpan.Zero;
        }

        public string ShiftTimeEndStr
        {
            get => ShiftTimeEnd.ToString(@"hh\:mm");
            set => ShiftTimeEnd = TimeSpan.TryParse(value, out var ts) ? ts : TimeSpan.Zero;
        }


        // Pump Sales
        /// <summary>
        ///    Shift Check Out: (1 to Many) Employee Names
        ///    Gas Types: K1, OffRoadDiesel, UnleadedPremium, UnleadedPlus, UnleadedRegular, Diesel
        ///    Daily Over/Short: (0 to Many) Employee name, Dollar Amount
        ///    Products: Ice, Beer, Groceries, Deli, Cigarettes, Meat, Non-Food, Donuts, Gasoline, Chicken, HBA, Lotto, Pizza, Money Order, Lottery, Farm Tax, Sales Tax
        ///    Less Credits: Credit Card, DebitCard, Food Stamps, Coupons, ApprovedCredit, Paid Outs, VoidAndRefund, ATM, Telecheck, Cash, Check, Total Credits
        /// </summary>
        [Precision(18, 2)]
        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal PumpK1Dollars { get; set; }

        [Precision(18, 2)]
        public decimal PumpK1Gallons { get; set; }

        [Precision(18, 2)]
        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal PumpOffRoadDieselDollars { get; set; }

        [Precision(18, 2)]
        public decimal PumpOffRoadDieselGallons { get; set; }

        [Precision(18, 2)]
        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal PumpUnleadedPremiumDollars { get; set; }

        [Precision(18, 2)]
        public decimal PumpUnleadedPremiumGallons { get; set; }

        [Precision(18, 2)]
        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal PumpUnleadedPlusDollars { get; set; }

        [Precision(18, 2)]
        public decimal PumpUnleadedPlusGallons { get; set; }

        [Precision(18, 2)]
        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal PumpUnleadedRegularDollars { get; set; }

        [Precision(18, 2)]
        public decimal PumpUnleadedRegularGallons { get; set; }

        [Precision(18, 2)]
        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal PumpDieselDollars { get; set; }

        [Precision(18, 2)]
        public decimal PumpDieselGallons { get; set; }

        public decimal TotalPumpSales => PumpK1Dollars + PumpOffRoadDieselDollars + PumpUnleadedPremiumDollars + PumpUnleadedPlusDollars + PumpUnleadedRegularDollars + PumpDieselDollars;
        public decimal TotalPumpGallons => PumpK1Gallons + PumpOffRoadDieselGallons + PumpUnleadedPremiumGallons + PumpUnleadedPlusGallons + PumpUnleadedRegularGallons + PumpDieselGallons;


        
        /// <summary>
        /// This section is for the sales of Products
        /// </summary>
        [Precision(18, 2)]
        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal Ice { get; set; }

        [Precision(18, 2)]
        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal Beer { get; set; }

        [Precision(18, 2)]
        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal Groceries { get; set; }
        [Precision(18, 2)]
        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal Deli { get; set; }
        [Precision(18, 2)]
        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal Cigarettes { get; set; }
        [Precision(18, 2)]
        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal Meat { get; set; }
        [Precision(18, 2)]
        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal NonFood { get; set; }
        [Precision(18, 2)]
        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal Donuts { get; set; }
        [Precision(18, 2)]
        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal Gasoline { get; set; }
        [Precision(18, 2)]
        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal Chicken { get; set; }
        [Precision(18, 2)]
        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal HBA { get; set; }
        [Precision(18, 2)]
        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal Lotto { get; set; }
        [Precision(18, 2)]
        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal Pizza { get; set; }
        [Precision(18, 2)]
        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal MoneyOrder { get; set; }

        [Precision(18, 2)]
        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal Lottery { get; set; }

        [Precision(18, 2)]
        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal FarmTax { get; set; }
        [Precision(18, 2)]
        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal SalesTax { get; set; }





        /// <summary>
        /// This section is for the Tender/Credit section of the end of day report.
        /// </summary>
        [Precision(18, 2)]
        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal CreditCard { get; set; }

        [Precision(18, 2)]
        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal DebitCard { get; set; }

        [Precision(18, 2)]
        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal FoodStamps { get; set; }

        [Precision(18, 2)]
        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal Coupons { get; set; }

        [Precision(18, 2)]
        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal ApprovedCredit { get; set; }

        [Precision(18, 2)]
        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal PaidOuts { get; set; }

        [Precision(18, 2)]
        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal VoidAndRefund { get; set; }

        [Precision(18, 2)]
        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal ATM { get; set; }

        [Precision(18, 2)]
        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal Telecheck { get; set; }

        [Precision(18, 2)]
        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal Cash { get; set; }

        [Precision(18, 2)]
        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal Check { get; set; }



        /// <summary>
        ///   tax report section
        /// </summary>
        [Precision(18, 2)]
        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal LowTaxTaxableSales { get; set; }

        [Precision(18, 2)]
        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal LowTaxNonTaxableSales { get; set; }

        [Precision(18, 2)]
        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal HighTaxTaxableSales { get; set; }

        [Precision(18, 2)]
        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal HighTaxNonTaxableSales { get; set; }



        /// Cashier Over/Short Entries
        public List<OverUnderEntry> OverUnderEntries { get; set; } = new();


        /// <summary>
        /// This s for storing notes related to the end of day report.
        /// </summary>
        [MaxLength(1000)]
        [DataType(DataType.MultilineText)]
        public string? Notes { get; set; }




        /// <summary>
        /// This section is for storing PDF files related to the end of day report.
        /// 
        /// </summary>
        public byte[]? CashDepositSlipPdf { get; set; }
        public string? CashDepositFileName { get; set; }


        public byte[]? PaidOutDepositSlipPdf { get; set; }
        public string? PaidOutDepositFileName { get; set; }

        public byte[]? ApprovedCreditDepositSlipPdf { get; set; }
        public string? ApprovedCreditDepositFileName { get; set; }


        public byte[]? CheckDepositSlipPdf { get; set; }
        public string? CheckDepositFileName { get; set; }

        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            if (CreatedByEmployeeName == "")
            {
                yield return new ValidationResult(
                   "* Employee name is required.",
                   new[] { nameof(CreatedByEmployeeName) });
            }

            if (ApprovedCredit != 0 && ApprovedCreditDepositFileName == null)
            {
                yield return new ValidationResult(
                    "* Approved Credit deposit slip is required when Approved Credit is not zero.",
                    new[] { nameof(ApprovedCreditDepositFileName) });
            }

            if (Cash != 0 && CashDepositFileName == null)
            {
                yield return new ValidationResult(
                    "* Cash deposit slip is required when Cash is not zero.",
                    new[] { nameof(CashDepositFileName) });
            }

            if (Check != 0 && CheckDepositFileName == null)
            {
                yield return new ValidationResult(
                    "* Check deposit slip is required when Checks is not zero.",
                    new[] { nameof(CheckDepositFileName) });
            }

            if (PaidOuts != 0 && PaidOutDepositFileName == null)
            {
                yield return new ValidationResult(
                    "* Paid Out deposit slip is required when Paid Outs is not zero.",
                    new[] { nameof(PaidOutDepositFileName) });
            }

           
        }

        /// <summary>
        /// This section calculates the total tax amount based on FarmTax and SalesTax.
        /// 
        /// </summary>

        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal TotalTax => FarmTax + SalesTax;

        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal TotalProductsSales => Ice + Beer + Groceries + Deli + Cigarettes + Meat + NonFood + Donuts + TotalPumpSales + Chicken + HBA + Lotto + Pizza + MoneyOrder + Lottery + TotalTax;

        [Precision(18, 2)]
        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal TotalCredits => CreditCard + DebitCard + FoodStamps + Coupons + ApprovedCredit + PaidOuts + VoidAndRefund + ATM + Telecheck + Cash + Check;


        [Precision(18, 2)]
        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal TotalOverUnder => OverUnderEntries.Sum(e => e.Total);

        [DisplayFormat(DataFormatString = "{0:C}")]
        public decimal GrandTotal => TotalProductsSales;

        


    }
}
